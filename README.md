# 🚁 Thermal Drone Detection System

A comprehensive AI-powered drone detection system that combines thermal imaging with computer vision for accurate drone identification and automatic alarm triggering.

## 🌟 Features

### Advanced Detection Capabilities
- **Dual-Mode Detection**: Combines YOLO object detection with thermal analysis
- **Real-time Processing**: Processes both visible and thermal camera feeds simultaneously
- **High Accuracy**: Fusion of multiple detection methods for reduced false positives
- **Configurable Zones**: Define specific detection areas for monitoring

### Thermal Analysis
- **Hot Object Detection**: Identifies heat signatures characteristic of drones
- **Background Subtraction**: Filters out static thermal noise
- **Contrast Enhancement**: Optimizes thermal imagery for better detection
- **Temperature Thresholding**: Configurable thermal sensitivity

### Intelligent Alarm System
- **Automatic Alerts**: Triggers alarms when drones are detected with high confidence
- **Audio Notifications**: Plays customizable alarm sounds
- **Visual Alerts**: Web interface notifications and overlays
- **Cooldown Protection**: Prevents alarm spam with configurable intervals

### Web Interface
- **Live Video Feeds**: Real-time display of both visible and thermal cameras
- **Detection Overlays**: Visual bounding boxes around detected objects
- **Control Panel**: Start/stop detection, adjust settings, test alarms
- **Statistics Dashboard**: Detection counts, rates, and history
- **Alert Log**: Chronological list of all detection events

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the system files
# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configuration

Edit `config.py` to match your camera settings:

```python
# Camera settings
CAMERA_IP = "*************"  # Your thermal camera IP
CAMERA_USERNAME = "admin"
CAMERA_PASSWORD = "admin"

# Detection parameters
DETECTION_CONFIDENCE_THRESHOLD = 0.6  # Adjust sensitivity
THERMAL_TEMPERATURE_THRESHOLD = 5.0   # Thermal sensitivity
```

### 3. Run the System

```bash
# Basic usage
python main.py

# With custom camera IP
python main.py --camera-ip *************

# Test mode (verify everything works)
python main.py --test-mode

# Disable alarm for testing
python main.py --no-alarm
```

### 4. Access Web Interface

Open your browser and navigate to: `http://localhost:5000`

## 🎛️ Web Interface Guide

### Main Dashboard
- **Video Feeds**: Left panel shows visible camera, right panel shows thermal
- **Status Bar**: System status, camera connection, and alarm status
- **Detection Overlays**: Red boxes appear around detected drones

### Control Panel
- **Start/Stop Detection**: Begin or halt the detection process
- **Test Alarm**: Verify alarm system functionality
- **Enable/Disable Alarm**: Toggle automatic alarm triggering
- **Confidence Threshold**: Adjust detection sensitivity (0.1-1.0)

### Statistics Panel
- **Total Detections**: Cumulative count of all detections
- **Recent Detections**: Count in the last minute
- **Last Detection**: Timestamp of most recent detection
- **Detection Rate**: Percentage of frames with detections

### Alerts Section
- **Recent Alerts**: Chronological list of detection events
- **Alert Modal**: Pop-up notifications for high-confidence detections

## ⚙️ Configuration Options

### Camera Settings
```python
CAMERA_IP = "*************"           # Camera IP address
CAMERA_USERNAME = "admin"             # Camera login username
CAMERA_PASSWORD = "admin"             # Camera login password
CAMERA_STREAM_URL = "http://..."      # Custom stream URL
```

### Detection Parameters
```python
DETECTION_CONFIDENCE_THRESHOLD = 0.6  # Minimum confidence for alerts
THERMAL_TEMPERATURE_THRESHOLD = 5.0   # Thermal detection sensitivity
MIN_DRONE_SIZE = 20                   # Minimum object size (pixels)
MAX_DRONE_SIZE = 200                  # Maximum object size (pixels)
```

### Alarm Configuration
```python
ALARM_ENABLED = True                  # Enable/disable alarms
ALARM_DURATION = 5                    # Alarm duration (seconds)
ALARM_COOLDOWN = 10                   # Time between alarms (seconds)
ALARM_SOUND_FILE = "alarm.wav"        # Custom alarm sound
```

### Performance Tuning
```python
FRAME_SKIP = 1                        # Process every nth frame
MAX_FPS = 30                          # Maximum processing rate
RESIZE_FACTOR = 1.0                   # Scale factor for processing
```

## 🔧 Advanced Features

### Detection Zones
Define specific areas for monitoring:
```python
DETECTION_ZONES = [
    {"name": "perimeter", "coords": [0, 0, 1, 0.3], "enabled": True},
    {"name": "restricted", "coords": [0.2, 0.2, 0.8, 0.8], "enabled": True}
]
```

### Custom Models
Replace the default YOLO model with a custom-trained drone detection model:
```python
CUSTOM_DRONE_MODEL = "path/to/your/drone_model.pt"
```

### Logging and Storage
```python
LOG_LEVEL = "INFO"                    # DEBUG, INFO, WARNING, ERROR
SAVE_DETECTION_IMAGES = True          # Save images with detections
DETECTION_LOG_DIR = "detections"      # Directory for saved images
```

## 🛠️ Troubleshooting

### Camera Connection Issues
1. Verify camera IP address and credentials
2. Check network connectivity
3. Try alternative stream URLs in `camera_interface.py`
4. Ensure camera supports MJPEG or RTSP streams

### Detection Problems
1. Adjust confidence threshold (lower = more sensitive)
2. Check thermal temperature threshold
3. Verify lighting conditions for visible camera
4. Review detection zones configuration

### Performance Issues
1. Increase `FRAME_SKIP` to process fewer frames
2. Reduce `RESIZE_FACTOR` to process smaller images
3. Lower `MAX_FPS` to reduce processing load
4. Check system resources (CPU, memory)

### Alarm Not Working
1. Test alarm system: `python main.py --test-mode`
2. Check audio system initialization
3. Verify `ALARM_ENABLED = True` in config
4. Ensure alarm sound file exists

## 📊 System Requirements

### Hardware
- **CPU**: Multi-core processor (Intel i5 or equivalent)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space for logs and models
- **Network**: Ethernet connection to thermal camera

### Software
- **Python**: 3.8 or higher
- **Operating System**: Windows 10/11, Linux, macOS
- **Browser**: Chrome, Firefox, Safari, Edge

### Camera Compatibility
- **Thermal Cameras**: FLIR, Seek, Optris, or similar
- **Protocols**: HTTP/MJPEG, RTSP
- **Resolution**: 640x480 minimum, 1920x1080 recommended

## 🔒 Security Considerations

### Network Security
- Use secure passwords for camera access
- Consider VPN for remote access
- Implement firewall rules for camera network

### Data Privacy
- Detection images are stored locally only
- No data is transmitted to external servers
- Configure log retention policies as needed

### Access Control
- Web interface has no built-in authentication
- Consider adding reverse proxy with authentication
- Restrict network access to authorized users

## 📈 Performance Optimization

### For High-Traffic Areas
```python
FRAME_SKIP = 2                        # Process every 2nd frame
RESIZE_FACTOR = 0.5                   # Process at half resolution
DETECTION_CONFIDENCE_THRESHOLD = 0.7  # Higher threshold
```

### For Maximum Accuracy
```python
FRAME_SKIP = 1                        # Process every frame
RESIZE_FACTOR = 1.0                   # Full resolution
DETECTION_CONFIDENCE_THRESHOLD = 0.4  # Lower threshold
```

### For Battery-Powered Systems
```python
MAX_FPS = 10                          # Reduce processing rate
FRAME_SKIP = 3                        # Process every 3rd frame
THERMAL_NOISE_REDUCTION = False       # Disable heavy processing
```

## 🆘 Support

### Command Line Help
```bash
python main.py --help
```

### Test System
```bash
python main.py --test-mode
```

### Debug Mode
```bash
python main.py --debug
```

### Log Files
Check the `logs/` directory for detailed system logs and error messages.

---

**🚁 Stay vigilant, stay protected!**
