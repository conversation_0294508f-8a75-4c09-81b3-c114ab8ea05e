"""
Alarm system for drone detection alerts
"""

import pygame
import threading
import time
import logging
import os
from datetime import datetime
from config import *

class AlarmSystem:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.is_playing = False
        self.last_alarm_time = 0
        self.alarm_thread = None
        
        # Initialize pygame mixer for audio
        try:
            pygame.mixer.init()
            self.audio_enabled = True
            self.logger.info("Audio system initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize audio: {e}")
            self.audio_enabled = False
        
        # Create default alarm sound if not exists
        self.create_default_alarm()
    
    def create_default_alarm(self):
        """Create a default alarm sound if none exists"""
        if not os.path.exists(ALARM_SOUND_FILE):
            try:
                # Generate a simple beep sound
                import numpy as np
                from scipy.io import wavfile
                
                sample_rate = 44100
                duration = 1.0  # seconds
                frequency = 1000  # Hz
                
                t = np.linspace(0, duration, int(sample_rate * duration))
                # Create a beep with fade in/out to avoid clicks
                fade_samples = int(0.1 * sample_rate)
                beep = np.sin(2 * np.pi * frequency * t)
                
                # Apply fade in/out
                beep[:fade_samples] *= np.linspace(0, 1, fade_samples)
                beep[-fade_samples:] *= np.linspace(1, 0, fade_samples)
                
                # Convert to 16-bit
                beep = (beep * 32767).astype(np.int16)
                
                wavfile.write(ALARM_SOUND_FILE, sample_rate, beep)
                self.logger.info(f"Created default alarm sound: {ALARM_SOUND_FILE}")
                
            except Exception as e:
                self.logger.error(f"Failed to create default alarm sound: {e}")
    
    def trigger_alarm(self, detection_info=None):
        """Trigger alarm if conditions are met"""
        if not ALARM_ENABLED:
            return False
        
        current_time = time.time()
        
        # Check cooldown period
        if current_time - self.last_alarm_time < ALARM_COOLDOWN:
            return False
        
        # Check if already playing
        if self.is_playing:
            return False
        
        self.last_alarm_time = current_time
        
        # Log the alarm trigger
        self.logger.warning(f"DRONE DETECTED - ALARM TRIGGERED at {datetime.now()}")
        if detection_info:
            self.logger.info(f"Detection details: {detection_info}")
        
        # Start alarm in separate thread
        self.alarm_thread = threading.Thread(target=self._play_alarm)
        self.alarm_thread.daemon = True
        self.alarm_thread.start()
        
        return True
    
    def _play_alarm(self):
        """Play alarm sound (internal method)"""
        if not self.audio_enabled:
            self._visual_alarm()
            return
        
        try:
            self.is_playing = True
            
            if os.path.exists(ALARM_SOUND_FILE):
                # Load and play the alarm sound
                pygame.mixer.music.load(ALARM_SOUND_FILE)
                
                # Play for specified duration
                end_time = time.time() + ALARM_DURATION
                while time.time() < end_time and self.is_playing:
                    if not pygame.mixer.music.get_busy():
                        pygame.mixer.music.play()
                    time.sleep(0.1)
                
                pygame.mixer.music.stop()
            else:
                # Fallback to system beep
                self._system_beep()
                
        except Exception as e:
            self.logger.error(f"Error playing alarm: {e}")
            self._system_beep()
        finally:
            self.is_playing = False
    
    def _system_beep(self):
        """Fallback system beep"""
        try:
            import winsound
            for _ in range(int(ALARM_DURATION)):
                winsound.Beep(1000, 1000)  # 1000Hz for 1 second
        except ImportError:
            # Linux/Mac fallback
            for _ in range(int(ALARM_DURATION)):
                print("\a")  # Terminal bell
                time.sleep(1)
    
    def _visual_alarm(self):
        """Visual alarm when audio is not available"""
        self.is_playing = True
        end_time = time.time() + ALARM_DURATION
        
        while time.time() < end_time and self.is_playing:
            print("🚨 DRONE DETECTED! 🚨")
            time.sleep(0.5)
        
        self.is_playing = False
    
    def stop_alarm(self):
        """Stop the current alarm"""
        self.is_playing = False
        if self.audio_enabled:
            try:
                pygame.mixer.music.stop()
            except:
                pass
        self.logger.info("Alarm stopped")
    
    def test_alarm(self):
        """Test the alarm system"""
        self.logger.info("Testing alarm system...")
        original_cooldown = self.last_alarm_time
        self.last_alarm_time = 0  # Reset cooldown for test
        
        result = self.trigger_alarm({"test": True, "confidence": 1.0})
        
        if not result:
            self.last_alarm_time = original_cooldown
        
        return result
    
    def get_status(self):
        """Get alarm system status"""
        return {
            'enabled': ALARM_ENABLED,
            'audio_enabled': self.audio_enabled,
            'is_playing': self.is_playing,
            'last_alarm_time': self.last_alarm_time,
            'cooldown_remaining': max(0, ALARM_COOLDOWN - (time.time() - self.last_alarm_time))
        }
    
    def set_enabled(self, enabled):
        """Enable or disable the alarm system"""
        global ALARM_ENABLED
        ALARM_ENABLED = enabled
        if not enabled:
            self.stop_alarm()
        self.logger.info(f"Alarm system {'enabled' if enabled else 'disabled'}")
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_alarm()
        if self.audio_enabled:
            try:
                pygame.mixer.quit()
            except:
                pass
