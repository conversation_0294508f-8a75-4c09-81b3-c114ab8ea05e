"""
Camera interface for connecting to thermal camera and processing streams
"""

import cv2
import numpy as np
import requests
from requests.auth import HTTPBasicAuth
import time
import logging
from config import *

class ThermalCameraInterface:
    def __init__(self):
        self.visible_cap = None
        self.thermal_cap = None
        self.session = requests.Session()
        self.session.auth = HTTPBasicAuth(CAMERA_USERNAME, CAMERA_PASSWORD)
        self.logger = logging.getLogger(__name__)

        # PTZ tracking variables
        self.current_pan = 0
        self.current_tilt = 0
        self.current_zoom = 1
        self.tracking_target = None
        self.tracking_enabled = False
        self.last_detection_time = 0

        # Camera capabilities
        self.supports_ptz = False
        self.supports_thermal = False
        self.supports_visible = False

    def connect(self):
        """Connect to both visible and thermal camera streams"""
        try:
            # Extended list of camera stream URLs to try
            visible_urls = [
                CAMERA_STREAM_URL,
                f"http://{CAMERA_IP}/video",
                f"http://{CAMERA_IP}/mjpeg",
                f"http://{CAMERA_IP}:8080/video",
                f"http://{CAMERA_IP}:8080/mjpeg",
                f"http://{CAMERA_IP}/cgi-bin/mjpg/video.cgi",
                f"http://{CAMERA_IP}/videostream.cgi",
                f"http://{CAMERA_IP}/axis-cgi/mjpg/video.cgi",
                f"http://{CAMERA_IP}/live.sdp",
                f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/stream1",
                f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/live",
                f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/h264"
            ]

            thermal_urls = [
                THERMAL_STREAM_URL,
                f"http://{CAMERA_IP}/thermal",
                f"http://{CAMERA_IP}/thermal_mjpeg",
                f"http://{CAMERA_IP}:8080/thermal",
                f"http://{CAMERA_IP}/cgi-bin/thermal/video.cgi",
                f"http://{CAMERA_IP}/thermal_stream",
                f"http://{CAMERA_IP}/infrared",
                f"http://{CAMERA_IP}/ir_stream",
                f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/thermal",
                f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/ir"
            ]

            # Try to connect to visible stream
            self.logger.info("Attempting to connect to visible camera...")
            for i, url in enumerate(visible_urls):
                try:
                    self.logger.info(f"Trying visible URL {i+1}/{len(visible_urls)}: {url}")
                    self.visible_cap = cv2.VideoCapture(url)
                    if self.visible_cap.isOpened():
                        # Test if we can actually read frames
                        ret, frame = self.visible_cap.read()
                        if ret and frame is not None:
                            self.supports_visible = True
                            self.logger.info(f"✅ Visible camera connected: {url}")
                            break
                        else:
                            self.visible_cap.release()
                            self.visible_cap = None
                except Exception as e:
                    self.logger.debug(f"Failed to connect to {url}: {e}")
                    continue

            # Try to connect to thermal stream
            self.logger.info("Attempting to connect to thermal camera...")
            for i, url in enumerate(thermal_urls):
                try:
                    self.logger.info(f"Trying thermal URL {i+1}/{len(thermal_urls)}: {url}")
                    self.thermal_cap = cv2.VideoCapture(url)
                    if self.thermal_cap.isOpened():
                        # Test if we can actually read frames
                        ret, frame = self.thermal_cap.read()
                        if ret and frame is not None:
                            self.supports_thermal = True
                            self.logger.info(f"✅ Thermal camera connected: {url}")
                            break
                        else:
                            self.thermal_cap.release()
                            self.thermal_cap = None
                except Exception as e:
                    self.logger.debug(f"Failed to connect to {url}: {e}")
                    continue
            
            # Set camera properties
            if self.visible_cap and self.visible_cap.isOpened():
                self.visible_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.visible_cap.set(cv2.CAP_PROP_FPS, MAX_FPS)
                
            if self.thermal_cap and self.thermal_cap.isOpened():
                self.thermal_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.thermal_cap.set(cv2.CAP_PROP_FPS, MAX_FPS)
                
            self.logger.info("Camera interface initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to camera: {e}")
            return False
    
    def get_frames(self):
        """Get both visible and thermal frames"""
        visible_frame = None
        thermal_frame = None
        
        try:
            if self.visible_cap and self.visible_cap.isOpened():
                ret, visible_frame = self.visible_cap.read()
                if not ret:
                    visible_frame = None
                    
            if self.thermal_cap and self.thermal_cap.isOpened():
                ret, thermal_frame = self.thermal_cap.read()
                if not ret:
                    thermal_frame = None
                    
        except Exception as e:
            self.logger.error(f"Error reading frames: {e}")
            
        return visible_frame, thermal_frame
    
    def get_frame_from_url(self, url):
        """Get a single frame from URL (fallback method)"""
        try:
            response = self.session.get(url, timeout=5)
            if response.status_code == 200:
                img_array = np.asarray(bytearray(response.content), dtype=np.uint8)
                frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                return frame
        except Exception as e:
            self.logger.error(f"Error getting frame from URL {url}: {e}")
        return None
    
    def disconnect(self):
        """Disconnect from camera streams"""
        if self.visible_cap:
            self.visible_cap.release()
        if self.thermal_cap:
            self.thermal_cap.release()
        self.logger.info("Camera interface disconnected")
    
    def detect_camera_capabilities(self):
        """Detect camera capabilities (PTZ, thermal, etc.)"""
        try:
            # Test PTZ capabilities
            ptz_test_urls = [
                f"http://{CAMERA_IP}/cgi-bin/ptz.cgi",
                f"http://{CAMERA_IP}/ptz",
                f"http://{CAMERA_IP}/api/ptz",
                f"http://{CAMERA_IP}/onvif/ptz"
            ]

            for url in ptz_test_urls:
                try:
                    response = self.session.get(url, timeout=3)
                    if response.status_code == 200:
                        self.supports_ptz = True
                        self.logger.info(f"PTZ control detected at: {url}")
                        break
                except:
                    continue

            # Test thermal capability
            if self.thermal_cap and self.thermal_cap.isOpened():
                self.supports_thermal = True
                self.logger.info("Thermal camera capability detected")

            # Test visible capability
            if self.visible_cap and self.visible_cap.isOpened():
                self.supports_visible = True
                self.logger.info("Visible camera capability detected")

        except Exception as e:
            self.logger.error(f"Error detecting camera capabilities: {e}")

    def set_ptz_position(self, pan, tilt, zoom=None):
        """Set PTZ camera position"""
        if not self.supports_ptz:
            return False

        try:
            # Common PTZ control URLs
            ptz_urls = [
                f"http://{CAMERA_IP}/cgi-bin/ptz.cgi?action=start&channel=0&code=Position&arg1={pan}&arg2={tilt}&arg3={zoom or self.current_zoom}",
                f"http://{CAMERA_IP}/ptz?pan={pan}&tilt={tilt}&zoom={zoom or self.current_zoom}",
                f"http://{CAMERA_IP}/api/ptz/absolute?pan={pan}&tilt={tilt}&zoom={zoom or self.current_zoom}"
            ]

            for url in ptz_urls:
                try:
                    response = self.session.get(url, timeout=3)
                    if response.status_code == 200:
                        self.current_pan = pan
                        self.current_tilt = tilt
                        if zoom:
                            self.current_zoom = zoom
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Error setting PTZ position: {e}")
            return False

    def track_target(self, detection_bbox, frame_width, frame_height):
        """Track a target using PTZ controls"""
        if not self.supports_ptz or not self.tracking_enabled:
            return False

        try:
            x, y, w, h = detection_bbox

            # Calculate target center
            target_x = x + w / 2
            target_y = y + h / 2

            # Convert to normalized coordinates (-1 to 1)
            norm_x = (target_x / frame_width) * 2 - 1
            norm_y = (target_y / frame_height) * 2 - 1

            # Calculate required pan/tilt adjustments
            pan_adjustment = norm_x * 10  # Adjust sensitivity as needed
            tilt_adjustment = -norm_y * 10  # Invert Y axis

            # Apply smoothing to prevent jittery movement
            if self.tracking_target:
                prev_x, prev_y = self.tracking_target
                pan_adjustment = (pan_adjustment + prev_x) / 2
                tilt_adjustment = (tilt_adjustment + prev_y) / 2

            # Update tracking target
            self.tracking_target = (pan_adjustment, tilt_adjustment)

            # Calculate new PTZ position
            new_pan = self.current_pan + pan_adjustment
            new_tilt = self.current_tilt + tilt_adjustment

            # Clamp values to camera limits
            new_pan = max(-180, min(180, new_pan))
            new_tilt = max(-90, min(90, new_tilt))

            # Only move if adjustment is significant enough
            if abs(pan_adjustment) > 0.5 or abs(tilt_adjustment) > 0.5:
                return self.set_ptz_position(new_pan, new_tilt)

            return True

        except Exception as e:
            self.logger.error(f"Error tracking target: {e}")
            return False

    def enable_tracking(self, enabled=True):
        """Enable or disable automatic tracking"""
        self.tracking_enabled = enabled
        if enabled:
            self.logger.info("Automatic drone tracking enabled")
        else:
            self.logger.info("Automatic drone tracking disabled")
            self.tracking_target = None

    def zoom_to_target(self, detection_bbox, frame_width, frame_height):
        """Automatically zoom to keep target in optimal size"""
        if not self.supports_ptz:
            return False

        try:
            x, y, w, h = detection_bbox

            # Calculate target size as percentage of frame
            target_size = (w * h) / (frame_width * frame_height)

            # Optimal target size (5-15% of frame)
            optimal_min = 0.05
            optimal_max = 0.15

            zoom_adjustment = 0

            if target_size < optimal_min:
                # Target too small, zoom in
                zoom_adjustment = 0.1
            elif target_size > optimal_max:
                # Target too large, zoom out
                zoom_adjustment = -0.1

            if abs(zoom_adjustment) > 0.05:
                new_zoom = self.current_zoom + zoom_adjustment
                new_zoom = max(1, min(10, new_zoom))  # Clamp zoom level
                return self.set_ptz_position(self.current_pan, self.current_tilt, new_zoom)

            return True

        except Exception as e:
            self.logger.error(f"Error adjusting zoom: {e}")
            return False

    def get_ptz_status(self):
        """Get current PTZ status"""
        return {
            'supports_ptz': self.supports_ptz,
            'tracking_enabled': self.tracking_enabled,
            'current_pan': self.current_pan,
            'current_tilt': self.current_tilt,
            'current_zoom': self.current_zoom,
            'has_target': self.tracking_target is not None
        }

    def __del__(self):
        self.disconnect()
