"""
Camera interface for connecting to thermal camera and processing streams
"""

import cv2
import numpy as np
import requests
from requests.auth import HTTP<PERSON>asicAuth
import time
import logging
from config import *

class ThermalCameraInterface:
    def __init__(self):
        self.visible_cap = None
        self.thermal_cap = None
        self.session = requests.Session()
        self.session.auth = HTTPBasicAuth(CAMERA_USERNAME, CAMERA_PASSWORD)
        self.logger = logging.getLogger(__name__)
        
    def connect(self):
        """Connect to both visible and thermal camera streams"""
        try:
            # Try to connect to visible stream
            self.visible_cap = cv2.VideoCapture(CAMERA_STREAM_URL)
            if not self.visible_cap.isOpened():
                self.logger.warning("Could not open visible stream, trying alternative URL")
                # Try alternative stream URLs
                alt_urls = [
                    f"http://{CAMERA_IP}:8080/video",
                    f"http://{CAMERA_IP}/mjpeg",
                    f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/stream1"
                ]
                for url in alt_urls:
                    self.visible_cap = cv2.VideoCapture(url)
                    if self.visible_cap.isOpened():
                        break
            
            # Try to connect to thermal stream
            self.thermal_cap = cv2.VideoCapture(THERMAL_STREAM_URL)
            if not self.thermal_cap.isOpened():
                self.logger.warning("Could not open thermal stream, trying alternative URL")
                # Try alternative thermal URLs
                alt_thermal_urls = [
                    f"http://{CAMERA_IP}:8080/thermal",
                    f"http://{CAMERA_IP}/thermal_mjpeg",
                    f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/thermal"
                ]
                for url in alt_thermal_urls:
                    self.thermal_cap = cv2.VideoCapture(url)
                    if self.thermal_cap.isOpened():
                        break
            
            # Set camera properties
            if self.visible_cap and self.visible_cap.isOpened():
                self.visible_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.visible_cap.set(cv2.CAP_PROP_FPS, MAX_FPS)
                
            if self.thermal_cap and self.thermal_cap.isOpened():
                self.thermal_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.thermal_cap.set(cv2.CAP_PROP_FPS, MAX_FPS)
                
            self.logger.info("Camera interface initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to camera: {e}")
            return False
    
    def get_frames(self):
        """Get both visible and thermal frames"""
        visible_frame = None
        thermal_frame = None
        
        try:
            if self.visible_cap and self.visible_cap.isOpened():
                ret, visible_frame = self.visible_cap.read()
                if not ret:
                    visible_frame = None
                    
            if self.thermal_cap and self.thermal_cap.isOpened():
                ret, thermal_frame = self.thermal_cap.read()
                if not ret:
                    thermal_frame = None
                    
        except Exception as e:
            self.logger.error(f"Error reading frames: {e}")
            
        return visible_frame, thermal_frame
    
    def get_frame_from_url(self, url):
        """Get a single frame from URL (fallback method)"""
        try:
            response = self.session.get(url, timeout=5)
            if response.status_code == 200:
                img_array = np.asarray(bytearray(response.content), dtype=np.uint8)
                frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                return frame
        except Exception as e:
            self.logger.error(f"Error getting frame from URL {url}: {e}")
        return None
    
    def disconnect(self):
        """Disconnect from camera streams"""
        if self.visible_cap:
            self.visible_cap.release()
        if self.thermal_cap:
            self.thermal_cap.release()
        self.logger.info("Camera interface disconnected")
    
    def __del__(self):
        self.disconnect()
