"""
Configuration settings for the thermal drone detection system
"""

# Camera settings
CAMERA_IP = "*************"
CAMERA_USERNAME = "admin"
CAMERA_PASSWORD = "admin"
CAMERA_STREAM_URL = f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/stream1"
THERMAL_STREAM_URL = f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/stream2"  # Try stream2 for thermal

# Detection parameters
DETECTION_CONFIDENCE_THRESHOLD = 0.6
THERMAL_TEMPERATURE_THRESHOLD = 5.0  # Temperature difference for thermal detection
MIN_DRONE_SIZE = 20  # Minimum pixel size for drone detection
MAX_DRONE_SIZE = 200  # Maximum pixel size for drone detection

# Thermal processing
THERMAL_NOISE_REDUCTION = True
THERMAL_CONTRAST_ENHANCEMENT = True
BACKGROUND_SUBTRACTION = True

# Alarm settings
ALARM_ENABLED = True
ALARM_SOUND_FILE = "alarm.wav"
ALARM_DURATION = 5  # seconds
ALARM_COOLDOWN = 10  # seconds between alarms

# Web interface
WEB_PORT = 5000
WEB_HOST = "0.0.0.0"

# Model settings
YOLO_MODEL_PATH = "yolov8n.pt"  # Will download if not exists
CUSTOM_DRONE_MODEL = "drone_model.pt"  # Custom trained model if available

# Detection zones (normalized coordinates 0-1)
DETECTION_ZONES = [
    {"name": "full_frame", "coords": [0, 0, 1, 1], "enabled": True},
    {"name": "perimeter", "coords": [0, 0, 1, 0.3], "enabled": False}
]

# Logging
LOG_LEVEL = "INFO"
LOG_DETECTIONS = True
SAVE_DETECTION_IMAGES = True
DETECTION_LOG_DIR = "detections"

# Performance
FRAME_SKIP = 1  # Process every nth frame
MAX_FPS = 30
RESIZE_FACTOR = 1.0  # Scale factor for processing (1.0 = original size)

# PTZ Tracking Settings
PTZ_TRACKING_ENABLED = True          # Enable automatic PTZ tracking
PTZ_SENSITIVITY = 10                 # Pan/tilt sensitivity (degrees per movement)
PTZ_SMOOTHING = 0.5                  # Movement smoothing factor (0-1)
PTZ_MIN_MOVEMENT = 0.5               # Minimum movement threshold (degrees)
PTZ_MAX_PAN = 180                    # Maximum pan angle (degrees)
PTZ_MAX_TILT = 90                    # Maximum tilt angle (degrees)
PTZ_MAX_ZOOM = 10                    # Maximum zoom level
PTZ_OPTIMAL_TARGET_SIZE = 0.1        # Optimal target size as fraction of frame (0.05-0.15)
