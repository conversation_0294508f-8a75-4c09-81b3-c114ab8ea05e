"""
Advanced drone detection system combining YOLO object detection with thermal analysis
"""

import cv2
import numpy as np
from ultralytics import YOLO
import torch
import logging
import time
from datetime import datetime
import os
from config import *
from thermal_processor import ThermalProcessor

class DroneDetector:
    def __init__(self):
        self.yolo_model = None
        self.thermal_processor = ThermalProcessor()
        self.logger = logging.getLogger(__name__)
        self.detection_history = []
        self.last_detection_time = 0
        
        # Create detection log directory
        if SAVE_DETECTION_IMAGES and not os.path.exists(DETECTION_LOG_DIR):
            os.makedirs(DETECTION_LOG_DIR)
        
        self.load_models()
    
    def load_models(self):
        """Load YOLO and custom drone detection models"""
        try:
            # Load YOLO model
            self.yolo_model = YOLO(YOLO_MODEL_PATH)
            self.logger.info(f"YOLO model loaded: {YOLO_MODEL_PATH}")
            
            # Try to load custom drone model if available
            if os.path.exists(CUSTOM_DRONE_MODEL):
                self.custom_model = YOLO(CUSTOM_DRONE_MODEL)
                self.logger.info(f"Custom drone model loaded: {CUSTOM_DRONE_MODEL}")
            else:
                self.custom_model = None
                self.logger.info("No custom drone model found, using YOLO only")
                
        except Exception as e:
            self.logger.error(f"Error loading models: {e}")
            self.yolo_model = None
            self.custom_model = None
    
    def detect_drones_yolo(self, frame):
        """Detect drones using YOLO object detection"""
        if self.yolo_model is None or frame is None:
            return []
        
        try:
            # Run YOLO detection
            results = self.yolo_model(frame, conf=DETECTION_CONFIDENCE_THRESHOLD)
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class name
                        class_id = int(box.cls[0])
                        class_name = self.yolo_model.names[class_id]
                        confidence = float(box.conf[0])
                        
                        # Check if it's a potential drone (bird, airplane, kite, etc.)
                        drone_classes = ['bird', 'airplane', 'kite', 'frisbee']
                        if class_name.lower() in drone_classes or 'drone' in class_name.lower():
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            
                            detections.append({
                                'bbox': (int(x1), int(y1), int(x2-x1), int(y2-y1)),
                                'confidence': confidence,
                                'class': class_name,
                                'source': 'yolo'
                            })
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error in YOLO detection: {e}")
            return []
    
    def detect_drones_thermal(self, thermal_frame):
        """Detect drones using thermal analysis"""
        if thermal_frame is None:
            return []
        
        try:
            hot_objects = self.thermal_processor.detect_hot_objects(thermal_frame)
            
            thermal_detections = []
            for obj in hot_objects:
                if obj['confidence'] > 0.3:  # Thermal confidence threshold
                    thermal_detections.append({
                        'bbox': obj['bbox'],
                        'confidence': obj['confidence'],
                        'class': 'thermal_object',
                        'source': 'thermal',
                        'temperature': obj['avg_temperature']
                    })
            
            return thermal_detections
            
        except Exception as e:
            self.logger.error(f"Error in thermal detection: {e}")
            return []
    
    def fuse_detections(self, yolo_detections, thermal_detections):
        """Fuse YOLO and thermal detections for improved accuracy"""
        if not yolo_detections and not thermal_detections:
            return []
        
        fused_detections = []
        
        # Add YOLO detections
        for detection in yolo_detections:
            fused_detections.append(detection)
        
        # Add thermal detections that don't overlap with YOLO
        for thermal_det in thermal_detections:
            overlaps = False
            for yolo_det in yolo_detections:
                if self.calculate_iou(thermal_det['bbox'], yolo_det['bbox']) > 0.3:
                    overlaps = True
                    # Boost confidence if thermal and YOLO agree
                    for fused_det in fused_detections:
                        if fused_det == yolo_det:
                            fused_det['confidence'] = min(1.0, fused_det['confidence'] * 1.3)
                            fused_det['thermal_support'] = True
                    break
            
            if not overlaps:
                fused_detections.append(thermal_det)
        
        return fused_detections
    
    def calculate_iou(self, box1, box2):
        """Calculate Intersection over Union of two bounding boxes"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2
        
        # Calculate intersection
        xi1 = max(x1, x2)
        yi1 = max(y1, y2)
        xi2 = min(x1 + w1, x2 + w2)
        yi2 = min(y1 + h1, y2 + h2)
        
        if xi2 <= xi1 or yi2 <= yi1:
            return 0
        
        intersection = (xi2 - xi1) * (yi2 - yi1)
        union = w1 * h1 + w2 * h2 - intersection
        
        return intersection / union if union > 0 else 0
    
    def detect(self, visible_frame, thermal_frame, camera_interface=None):
        """Main detection function combining all methods"""
        detections = []

        try:
            # YOLO detection on visible frame
            yolo_detections = []
            if visible_frame is not None:
                yolo_detections = self.detect_drones_yolo(visible_frame)

            # Thermal detection
            thermal_detections = []
            if thermal_frame is not None:
                thermal_detections = self.detect_drones_thermal(thermal_frame)

            # Fuse detections
            detections = self.fuse_detections(yolo_detections, thermal_detections)

            # Filter by detection zones
            detections = self.filter_by_zones(detections, visible_frame)

            # Apply tracking if camera interface supports PTZ
            if detections and camera_interface:
                self.apply_tracking(detections, visible_frame, camera_interface)

            # Update detection history
            if detections:
                self.last_detection_time = time.time()
                self.detection_history.append({
                    'timestamp': datetime.now(),
                    'count': len(detections),
                    'detections': detections
                })

                # Keep only recent history
                cutoff_time = datetime.now().timestamp() - 300  # 5 minutes
                self.detection_history = [
                    h for h in self.detection_history
                    if h['timestamp'].timestamp() > cutoff_time
                ]

                # Save detection images if enabled
                if SAVE_DETECTION_IMAGES:
                    self.save_detection_image(visible_frame, thermal_frame, detections)

            return detections

        except Exception as e:
            self.logger.error(f"Error in detection: {e}")
            return []

    def apply_tracking(self, detections, frame, camera_interface):
        """Apply automatic tracking to the highest confidence detection"""
        if not detections or not camera_interface.supports_ptz:
            return

        try:
            # Find the highest confidence detection
            best_detection = max(detections, key=lambda d: d['confidence'])

            if best_detection['confidence'] >= DETECTION_CONFIDENCE_THRESHOLD:
                frame_height, frame_width = frame.shape[:2]

                # Track the target
                camera_interface.track_target(
                    best_detection['bbox'],
                    frame_width,
                    frame_height
                )

                # Adjust zoom to keep target in optimal size
                camera_interface.zoom_to_target(
                    best_detection['bbox'],
                    frame_width,
                    frame_height
                )

                self.logger.debug(f"Tracking drone with confidence {best_detection['confidence']:.2f}")

        except Exception as e:
            self.logger.error(f"Error applying tracking: {e}")
    
    def filter_by_zones(self, detections, frame):
        """Filter detections by configured detection zones"""
        if frame is None:
            return detections
        
        h, w = frame.shape[:2]
        filtered_detections = []
        
        for detection in detections:
            x, y, bbox_w, bbox_h = detection['bbox']
            center_x = (x + bbox_w/2) / w
            center_y = (y + bbox_h/2) / h
            
            for zone in DETECTION_ZONES:
                if not zone['enabled']:
                    continue
                    
                zx1, zy1, zx2, zy2 = zone['coords']
                if zx1 <= center_x <= zx2 and zy1 <= center_y <= zy2:
                    detection['zone'] = zone['name']
                    filtered_detections.append(detection)
                    break
        
        return filtered_detections
    
    def save_detection_image(self, visible_frame, thermal_frame, detections):
        """Save image with detections for analysis"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if visible_frame is not None:
                # Draw detections on visible frame
                annotated_frame = visible_frame.copy()
                for detection in detections:
                    x, y, w, h = detection['bbox']
                    confidence = detection['confidence']
                    label = f"{detection['class']}: {confidence:.2f}"
                    
                    cv2.rectangle(annotated_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                    cv2.putText(annotated_frame, label, (x, y-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                filename = os.path.join(DETECTION_LOG_DIR, f"detection_{timestamp}.jpg")
                cv2.imwrite(filename, annotated_frame)
                
        except Exception as e:
            self.logger.error(f"Error saving detection image: {e}")
    
    def get_detection_stats(self):
        """Get detection statistics"""
        if not self.detection_history:
            return {
                'total_detections': 0,
                'recent_detections': 0,
                'last_detection': None,
                'detection_rate': 0
            }
        
        total_detections = sum(h['count'] for h in self.detection_history)
        recent_detections = sum(
            h['count'] for h in self.detection_history 
            if (datetime.now() - h['timestamp']).seconds < 60
        )
        
        return {
            'total_detections': total_detections,
            'recent_detections': recent_detections,
            'last_detection': self.detection_history[-1]['timestamp'],
            'detection_rate': len(self.detection_history) / max(1, len(self.detection_history))
        }
