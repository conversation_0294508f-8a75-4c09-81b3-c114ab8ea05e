2025-06-01 22:46:56,767 - root - INFO - Starting Thermal Drone Detection System
2025-06-01 22:46:57,327 - drone_detector - INFO - YOLO model loaded: yolov8n.pt
2025-06-01 22:46:57,327 - drone_detector - INFO - No custom drone model found, using YOLO only
2025-06-01 22:46:57,525 - alarm_system - INFO - Audio system initialized
2025-06-01 22:46:57,527 - root - INFO - System initialization complete
2025-06-01 22:46:57,527 - web_interface - INFO - Starting web interface on 0.0.0.0:5000
2025-06-01 22:46:59,556 - web_interface - INFO - Client connected
2025-06-01 22:46:59,562 - engineio.server - ERROR - post request handler error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\server.py", line 307, in handle_request
    socket.handle_post_request(environ)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\socket.py", line 113, in handle_post_request
    p = payload.Payload(encoded_payload=body)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 13, in __init__
    self.decode(encoded_payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 44, in decode
    raise ValueError('Too many packets in payload')
ValueError: Too many packets in payload
