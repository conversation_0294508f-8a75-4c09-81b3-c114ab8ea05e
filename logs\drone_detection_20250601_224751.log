2025-06-01 22:47:51,991 - root - INFO - Starting Thermal Drone Detection System
2025-06-01 22:47:52,536 - drone_detector - INFO - YOLO model loaded: yolov8n.pt
2025-06-01 22:47:52,536 - drone_detector - INFO - No custom drone model found, using YOLO only
2025-06-01 22:47:52,728 - alarm_system - INFO - Audio system initialized
2025-06-01 22:47:52,728 - root - INFO - System initialization complete
2025-06-01 22:47:52,728 - web_interface - INFO - Starting web interface on 0.0.0.0:5000
2025-06-01 22:47:56,553 - web_interface - INFO - Client connected
2025-06-01 22:47:56,558 - engineio.server - ERROR - post request handler error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\server.py", line 307, in handle_request
    socket.handle_post_request(environ)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\socket.py", line 113, in handle_post_request
    p = payload.Payload(encoded_payload=body)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 13, in __init__
    self.decode(encoded_payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 44, in decode
    raise ValueError('Too many packets in payload')
ValueError: Too many packets in payload
2025-06-01 22:48:56,558 - web_interface - INFO - Client disconnected
2025-06-01 22:48:58,246 - web_interface - INFO - Client connected
