2025-06-01 22:49:56,561 - root - INFO - Starting Thermal Drone Detection System
2025-06-01 22:49:57,116 - drone_detector - INFO - YOLO model loaded: yolov8n.pt
2025-06-01 22:49:57,116 - drone_detector - INFO - No custom drone model found, using YOLO only
2025-06-01 22:49:57,320 - alarm_system - INFO - Audio system initialized
2025-06-01 22:49:57,323 - root - INFO - System initialization complete
2025-06-01 22:49:57,323 - web_interface - INFO - Starting web interface on 0.0.0.0:5000
2025-06-01 22:50:00,910 - web_interface - INFO - C<PERSON> connected
2025-06-01 23:56:26,304 - web_interface - INFO - <PERSON><PERSON> disconnected
2025-06-01 23:57:18,556 - web_interface - INFO - Client connected
2025-06-01 23:57:18,563 - engineio.server - ERROR - post request handler error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\server.py", line 307, in handle_request
    socket.handle_post_request(environ)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\socket.py", line 113, in handle_post_request
    p = payload.Payload(encoded_payload=body)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 13, in __init__
    self.decode(encoded_payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\engineio\payload.py", line 44, in decode
    raise ValueError('Too many packets in payload')
ValueError: Too many packets in payload
2025-06-01 23:59:03,239 - web_interface - INFO - Client disconnected
2025-06-01 23:59:05,246 - web_interface - INFO - Client connected
