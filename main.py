"""
Main entry point for the Thermal Drone Detection System
"""

import logging
import sys
import os
import argparse
from datetime import datetime
from config import *
from web_interface import WebInterface

def setup_logging():
    """Setup logging configuration"""
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Setup logging format
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Setup file handler
    log_filename = f"logs/drone_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(getattr(logging, LOG_LEVEL))
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, LOG_LEVEL))
    console_handler.setFormatter(logging.Formatter(log_format))
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        DETECTION_LOG_DIR,
        'models'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import cv2
        import numpy
        import torch
        import ultralytics
        import flask
        import pygame
        print("✓ All required dependencies are available")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install dependencies using: pip install -r requirements.txt")
        return False

def print_system_info():
    """Print system information and configuration"""
    print("=" * 60)
    print("🚁 THERMAL DRONE DETECTION SYSTEM")
    print("=" * 60)
    print(f"Camera IP: {CAMERA_IP}")
    print(f"Web Interface: http://localhost:{WEB_PORT}")
    print(f"Detection Confidence Threshold: {DETECTION_CONFIDENCE_THRESHOLD}")
    print(f"Alarm Enabled: {ALARM_ENABLED}")
    print(f"Log Level: {LOG_LEVEL}")
    print(f"Detection Log Directory: {DETECTION_LOG_DIR}")
    print("=" * 60)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Thermal Drone Detection System')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--camera-ip', help='Camera IP address', default=CAMERA_IP)
    parser.add_argument('--web-port', type=int, help='Web interface port', default=WEB_PORT)
    parser.add_argument('--no-alarm', action='store_true', help='Disable alarm system')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--test-mode', action='store_true', help='Run in test mode')
    
    args = parser.parse_args()
    
    # Update configuration based on arguments
    import config
    if args.camera_ip:
        config.CAMERA_IP = args.camera_ip

    if args.web_port:
        config.WEB_PORT = args.web_port

    if args.no_alarm:
        config.ALARM_ENABLED = False

    if args.debug:
        config.LOG_LEVEL = 'DEBUG'
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting Thermal Drone Detection System")
    
    # Create necessary directories
    create_directories()
    
    # Print system information
    print_system_info()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Test mode
    if args.test_mode:
        logger.info("Running in test mode")
        run_tests()
        return
    
    try:
        # Create and run the web interface
        web_interface = WebInterface()
        
        logger.info("System initialization complete")
        print("\n🚀 System ready! Open your browser and go to:")
        print(f"   http://localhost:{WEB_PORT}")
        print("\nPress Ctrl+C to stop the system")
        
        # Run the web interface
        web_interface.run()
        
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
        print("\n🛑 Shutting down system...")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
    
    finally:
        logger.info("System shutdown complete")
        print("✅ System shutdown complete")

def run_tests():
    """Run system tests"""
    print("\n🧪 Running system tests...")
    
    # Test camera connection
    print("Testing camera connection...")
    from camera_interface import ThermalCameraInterface
    camera = ThermalCameraInterface()
    if camera.connect():
        print("✓ Camera connection successful")
        camera.disconnect()
    else:
        print("✗ Camera connection failed")
    
    # Test alarm system
    print("Testing alarm system...")
    from alarm_system import AlarmSystem
    alarm = AlarmSystem()
    if alarm.test_alarm():
        print("✓ Alarm system test successful")
    else:
        print("✗ Alarm system test failed")
    
    # Test detection models
    print("Testing detection models...")
    try:
        from drone_detector import DroneDetector
        detector = DroneDetector()
        print("✓ Detection models loaded successfully")
    except Exception as e:
        print(f"✗ Detection models test failed: {e}")
    
    print("🧪 Tests complete")

if __name__ == '__main__':
    main()
