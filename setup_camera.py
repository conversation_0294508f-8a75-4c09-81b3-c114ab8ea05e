"""
Camera setup and configuration utility for the Thermal Drone Detection System
"""

import cv2
import requests
from requests.auth import HTTPBasicAuth
import time
import sys

def test_camera_connection(ip, username, password):
    """Test various camera connection methods"""
    print(f"Testing camera connection to {ip}...")
    
    # Common camera stream URLs to try
    test_urls = [
        f"http://{ip}/video",
        f"http://{ip}/mjpeg",
        f"http://{ip}:8080/video",
        f"http://{ip}:8080/mjpeg",
        f"http://{ip}/cgi-bin/mjpg/video.cgi",
        f"http://{ip}/videostream.cgi",
        f"rtsp://{username}:{password}@{ip}/stream1",
        f"rtsp://{username}:{password}@{ip}/live",
        f"rtsp://{username}:{password}@{ip}/h264",
    ]
    
    thermal_urls = [
        f"http://{ip}/thermal",
        f"http://{ip}/thermal_mjpeg",
        f"http://{ip}:8080/thermal",
        f"http://{ip}/cgi-bin/thermal/video.cgi",
        f"rtsp://{username}:{password}@{ip}/thermal",
    ]
    
    working_urls = []
    
    print("\n🔍 Testing visible camera streams...")
    for url in test_urls:
        try:
            print(f"  Trying: {url}")
            
            if url.startswith('http'):
                # Test HTTP/MJPEG stream
                session = requests.Session()
                session.auth = HTTPBasicAuth(username, password)
                response = session.get(url, timeout=5, stream=True)
                if response.status_code == 200:
                    print(f"  ✅ HTTP stream working: {url}")
                    working_urls.append(('visible', url))
                else:
                    print(f"  ❌ HTTP error {response.status_code}")
            else:
                # Test RTSP stream
                cap = cv2.VideoCapture(url)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        print(f"  ✅ RTSP stream working: {url}")
                        working_urls.append(('visible', url))
                    else:
                        print(f"  ❌ RTSP stream opened but no frames")
                    cap.release()
                else:
                    print(f"  ❌ RTSP stream failed to open")
                    
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        time.sleep(0.5)  # Brief pause between tests
    
    print("\n🌡️ Testing thermal camera streams...")
    for url in thermal_urls:
        try:
            print(f"  Trying: {url}")
            
            if url.startswith('http'):
                session = requests.Session()
                session.auth = HTTPBasicAuth(username, password)
                response = session.get(url, timeout=5, stream=True)
                if response.status_code == 200:
                    print(f"  ✅ Thermal HTTP stream working: {url}")
                    working_urls.append(('thermal', url))
                else:
                    print(f"  ❌ HTTP error {response.status_code}")
            else:
                cap = cv2.VideoCapture(url)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        print(f"  ✅ Thermal RTSP stream working: {url}")
                        working_urls.append(('thermal', url))
                    else:
                        print(f"  ❌ RTSP stream opened but no frames")
                    cap.release()
                else:
                    print(f"  ❌ RTSP stream failed to open")
                    
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        time.sleep(0.5)
    
    return working_urls

def test_basic_connectivity(ip):
    """Test basic network connectivity to camera"""
    print(f"\n🌐 Testing basic connectivity to {ip}...")
    
    try:
        # Try to ping the camera
        import subprocess
        import platform
        
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        command = ['ping', param, '1', ip]
        
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Ping successful to {ip}")
            return True
        else:
            print(f"  ❌ Ping failed to {ip}")
            return False
            
    except Exception as e:
        print(f"  ❌ Ping test error: {e}")
        return False

def test_web_interface(ip, username, password):
    """Test camera web interface"""
    print(f"\n🌐 Testing camera web interface...")
    
    test_paths = [
        "/",
        "/index.html",
        "/live.html",
        "/viewer.html",
        "/admin",
    ]
    
    for path in test_paths:
        try:
            url = f"http://{ip}{path}"
            print(f"  Trying: {url}")
            
            session = requests.Session()
            session.auth = HTTPBasicAuth(username, password)
            response = session.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"  ✅ Web interface accessible: {url}")
                return url
            else:
                print(f"  ❌ HTTP error {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    return None

def generate_config(working_urls, ip, username, password):
    """Generate configuration based on test results"""
    print(f"\n📝 Generating configuration...")
    
    visible_url = None
    thermal_url = None
    
    for stream_type, url in working_urls:
        if stream_type == 'visible' and visible_url is None:
            visible_url = url
        elif stream_type == 'thermal' and thermal_url is None:
            thermal_url = url
    
    config_content = f'''# Camera Configuration - Generated by setup_camera.py
# Generated on: {time.strftime("%Y-%m-%d %H:%M:%S")}

# Camera settings
CAMERA_IP = "{ip}"
CAMERA_USERNAME = "{username}"
CAMERA_PASSWORD = "{password}"
'''
    
    if visible_url:
        config_content += f'CAMERA_STREAM_URL = "{visible_url}"\n'
    else:
        config_content += f'CAMERA_STREAM_URL = "http://{ip}/video"  # Default - may need adjustment\n'
    
    if thermal_url:
        config_content += f'THERMAL_STREAM_URL = "{thermal_url}"\n'
    else:
        config_content += f'THERMAL_STREAM_URL = "http://{ip}/thermal"  # Default - may need adjustment\n'
    
    config_content += '''
# Detection parameters (adjust as needed)
DETECTION_CONFIDENCE_THRESHOLD = 0.6
THERMAL_TEMPERATURE_THRESHOLD = 5.0
MIN_DRONE_SIZE = 20
MAX_DRONE_SIZE = 200

# Alarm settings
ALARM_ENABLED = True
ALARM_DURATION = 5
ALARM_COOLDOWN = 10
'''
    
    # Save to file
    with open('camera_config.py', 'w') as f:
        f.write(config_content)
    
    print(f"  ✅ Configuration saved to camera_config.py")
    print(f"  📋 Copy the working URLs to your config.py file")

def main():
    """Main setup function"""
    print("🚁 Thermal Drone Detection System - Camera Setup")
    print("=" * 60)
    
    # Get camera details from user
    ip = input("Enter camera IP address (default: *************): ").strip()
    if not ip:
        ip = "*************"
    
    username = input("Enter camera username (default: admin): ").strip()
    if not username:
        username = "admin"
    
    password = input("Enter camera password (default: admin): ").strip()
    if not password:
        password = "admin"
    
    print(f"\n🔧 Testing camera: {ip} with credentials {username}:{password}")
    
    # Test basic connectivity
    if not test_basic_connectivity(ip):
        print("\n❌ Basic connectivity failed. Please check:")
        print("  - Camera IP address is correct")
        print("  - Camera is powered on and connected to network")
        print("  - Network connectivity between computer and camera")
        return
    
    # Test web interface
    web_url = test_web_interface(ip, username, password)
    if web_url:
        print(f"\n🌐 Camera web interface: {web_url}")
    
    # Test camera streams
    working_urls = test_camera_connection(ip, username, password)
    
    if working_urls:
        print(f"\n✅ Found {len(working_urls)} working stream(s):")
        for stream_type, url in working_urls:
            print(f"  {stream_type.upper()}: {url}")
        
        generate_config(working_urls, ip, username, password)
        
        print(f"\n🎉 Setup complete! Next steps:")
        print(f"  1. Review the generated camera_config.py file")
        print(f"  2. Copy the working URLs to your main config.py")
        print(f"  3. Run the system: python main.py --test-mode")
        
    else:
        print(f"\n❌ No working camera streams found. Please check:")
        print(f"  - Camera supports MJPEG or RTSP streaming")
        print(f"  - Username and password are correct")
        print(f"  - Camera streaming is enabled in camera settings")
        print(f"  - Firewall is not blocking camera ports")
        
        print(f"\n💡 Manual configuration suggestions:")
        print(f"  - Check camera manual for correct stream URLs")
        print(f"  - Try accessing camera web interface at http://{ip}")
        print(f"  - Verify camera model and streaming capabilities")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ Setup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n\n❌ Setup error: {e}")
        sys.exit(1)
