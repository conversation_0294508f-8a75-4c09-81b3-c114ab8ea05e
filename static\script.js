// Thermal Drone Detection System JavaScript

class DroneDetectionUI {
    constructor() {
        this.socket = io();
        this.isDetectionRunning = false;
        this.alertModal = document.getElementById('alert-modal');
        this.setupEventListeners();
        this.setupSocketListeners();
        this.updateUI();
    }

    setupEventListeners() {
        // Control buttons
        document.getElementById('start-detection').addEventListener('click', () => {
            this.startDetection();
        });

        document.getElementById('stop-detection').addEventListener('click', () => {
            this.stopDetection();
        });

        document.getElementById('test-alarm').addEventListener('click', () => {
            this.testAlarm();
        });

        // Settings
        document.getElementById('alarm-enabled').addEventListener('change', (e) => {
            this.socket.emit('set_alarm_enabled', e.target.checked);
        });

        document.getElementById('save-detections').addEventListener('change', (e) => {
            this.socket.emit('set_save_detections', e.target.checked);
        });

        document.getElementById('confidence-threshold').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('confidence-value').textContent = value.toFixed(1);
            this.socket.emit('set_confidence_threshold', value);
        });

        document.getElementById('auto-tracking').addEventListener('change', (e) => {
            this.socket.emit('enable_tracking', e.target.checked);
        });

        // PTZ Controls
        this.setupPTZControls();

        // Modal controls
        document.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('acknowledge-alert').addEventListener('click', () => {
            this.acknowledgeAlert();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === this.alertModal) {
                this.closeModal();
            }
        });
    }

    setupSocketListeners() {
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('system-status', 'Connected', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('system-status', 'Disconnected', 'error');
        });

        this.socket.on('camera_status', (data) => {
            this.updateStatus('camera-status', data.status, data.connected ? 'success' : 'error');
        });

        this.socket.on('detection_status', (data) => {
            this.isDetectionRunning = data.running;
            this.updateDetectionButtons();
        });

        this.socket.on('video_frame', (data) => {
            this.updateVideoFeed(data);
        });

        this.socket.on('detection_result', (data) => {
            this.handleDetectionResult(data);
        });

        this.socket.on('alarm_triggered', (data) => {
            this.showAlarmModal(data);
        });

        this.socket.on('stats_update', (data) => {
            this.updateStats(data);
        });

        this.socket.on('alarm_status', (data) => {
            this.updateAlarmStatus(data);
        });

        this.socket.on('tracking_status', (data) => {
            this.updateTrackingStatus(data);
        });

        this.socket.on('ptz_result', (data) => {
            this.updatePTZPosition(data);
        });
    }

    startDetection() {
        this.socket.emit('start_detection');
        this.updateStatus('system-status', 'Starting...', 'warning');
    }

    stopDetection() {
        this.socket.emit('stop_detection');
        this.updateStatus('system-status', 'Stopping...', 'warning');
    }

    testAlarm() {
        this.socket.emit('test_alarm');
    }

    updateStatus(elementId, text, type) {
        const element = document.getElementById(elementId);
        element.textContent = text;
        element.className = `status-value ${type}`;
    }

    updateDetectionButtons() {
        const startBtn = document.getElementById('start-detection');
        const stopBtn = document.getElementById('stop-detection');
        
        if (this.isDetectionRunning) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            this.updateStatus('system-status', 'Running', 'success');
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            this.updateStatus('system-status', 'Stopped', 'error');
        }
    }

    updateVideoFeed(data) {
        if (data.visible_frame) {
            document.getElementById('visible-feed').src = 'data:image/jpeg;base64,' + data.visible_frame;
        }
        if (data.thermal_frame) {
            document.getElementById('thermal-feed').src = 'data:image/jpeg;base64,' + data.thermal_frame;
        }
    }

    handleDetectionResult(data) {
        // Clear previous detection overlays
        document.getElementById('visible-overlay').innerHTML = '';
        document.getElementById('thermal-overlay').innerHTML = '';

        // Draw detection boxes
        if (data.detections && data.detections.length > 0) {
            data.detections.forEach(detection => {
                this.drawDetectionBox(detection, data.frame_type);
            });

            // Add to alerts list
            this.addAlert(data);
        }
    }

    drawDetectionBox(detection, frameType) {
        const overlay = document.getElementById(`${frameType}-overlay`);
        const videoWrapper = overlay.parentElement;
        const rect = videoWrapper.getBoundingClientRect();
        
        const box = document.createElement('div');
        box.className = 'detection-box';
        
        // Convert detection coordinates to overlay coordinates
        const x = (detection.bbox[0] / detection.frame_width) * rect.width;
        const y = (detection.bbox[1] / detection.frame_height) * rect.height;
        const w = (detection.bbox[2] / detection.frame_width) * rect.width;
        const h = (detection.bbox[3] / detection.frame_height) * rect.height;
        
        box.style.left = x + 'px';
        box.style.top = y + 'px';
        box.style.width = w + 'px';
        box.style.height = h + 'px';
        
        // Add label
        const label = document.createElement('div');
        label.className = 'detection-label';
        label.textContent = `${detection.class} (${(detection.confidence * 100).toFixed(1)}%)`;
        box.appendChild(label);
        
        overlay.appendChild(box);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (box.parentElement) {
                box.parentElement.removeChild(box);
            }
        }, 3000);
    }

    addAlert(data) {
        const alertsList = document.getElementById('alerts-list');
        const alertItem = document.createElement('div');
        alertItem.className = 'alert-item';
        
        const time = new Date().toLocaleTimeString();
        const count = data.detections.length;
        const confidence = Math.max(...data.detections.map(d => d.confidence));
        
        alertItem.innerHTML = `
            <div class="alert-time">${time}</div>
            <div class="alert-message">
                ${count} drone(s) detected with ${(confidence * 100).toFixed(1)}% confidence
            </div>
        `;
        
        alertsList.insertBefore(alertItem, alertsList.firstChild);
        
        // Keep only last 10 alerts
        while (alertsList.children.length > 10) {
            alertsList.removeChild(alertsList.lastChild);
        }
    }

    showAlarmModal(data) {
        const alertDetails = document.getElementById('alert-details');
        alertDetails.innerHTML = `
            <p><strong>Time:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
            <p><strong>Detections:</strong> ${data.detection_count}</p>
            <p><strong>Highest Confidence:</strong> ${(data.max_confidence * 100).toFixed(1)}%</p>
            <p><strong>Location:</strong> ${data.zone || 'Unknown'}</p>
        `;
        
        this.alertModal.style.display = 'block';
        
        // Auto-close after 10 seconds
        setTimeout(() => {
            this.closeModal();
        }, 10000);
    }

    closeModal() {
        this.alertModal.style.display = 'none';
    }

    acknowledgeAlert() {
        this.socket.emit('acknowledge_alert');
        this.closeModal();
    }

    updateStats(data) {
        document.getElementById('total-detections').textContent = data.total_detections || 0;
        document.getElementById('recent-detections').textContent = data.recent_detections || 0;
        document.getElementById('last-detection').textContent = 
            data.last_detection ? new Date(data.last_detection).toLocaleString() : 'Never';
        document.getElementById('detection-rate').textContent = 
            ((data.detection_rate || 0) * 100).toFixed(1) + '%';
    }

    updateAlarmStatus(data) {
        const status = data.enabled ? (data.is_playing ? 'Playing' : 'Ready') : 'Disabled';
        const type = data.enabled ? (data.is_playing ? 'warning' : 'success') : 'error';
        this.updateStatus('alarm-status', status, type);
        
        // Update checkbox
        document.getElementById('alarm-enabled').checked = data.enabled;
    }

    updateUI() {
        // Initial UI update
        this.updateDetectionButtons();

        // Update every second
        setInterval(() => {
            this.socket.emit('get_status');
            this.socket.emit('get_ptz_status');
        }, 1000);
    }

    setupPTZControls() {
        // PTZ movement controls
        document.getElementById('tilt-up').addEventListener('click', () => {
            this.movePTZ(0, 10, 0);
        });

        document.getElementById('tilt-down').addEventListener('click', () => {
            this.movePTZ(0, -10, 0);
        });

        document.getElementById('pan-left').addEventListener('click', () => {
            this.movePTZ(-10, 0, 0);
        });

        document.getElementById('pan-right').addEventListener('click', () => {
            this.movePTZ(10, 0, 0);
        });

        document.getElementById('center').addEventListener('click', () => {
            this.movePTZ(0, 0, 0, true); // Reset to center
        });

        document.getElementById('zoom-in').addEventListener('click', () => {
            this.movePTZ(0, 0, 0.2);
        });

        document.getElementById('zoom-out').addEventListener('click', () => {
            this.movePTZ(0, 0, -0.2);
        });
    }

    movePTZ(panDelta, tiltDelta, zoomDelta, reset = false) {
        if (reset) {
            this.socket.emit('manual_ptz', { pan: 0, tilt: 0, zoom: 1 });
        } else {
            // Get current position and apply deltas
            const currentPan = parseFloat(document.getElementById('current-pan').textContent) || 0;
            const currentTilt = parseFloat(document.getElementById('current-tilt').textContent) || 0;
            const currentZoom = parseFloat(document.getElementById('zoom-level').textContent) || 1;

            const newPan = currentPan + panDelta;
            const newTilt = currentTilt + tiltDelta;
            const newZoom = Math.max(1, Math.min(10, currentZoom + zoomDelta));

            this.socket.emit('manual_ptz', {
                pan: newPan,
                tilt: newTilt,
                zoom: newZoom
            });
        }
    }

    updateTrackingStatus(data) {
        // Update PTZ support status
        const ptzSupport = document.getElementById('ptz-support');
        ptzSupport.textContent = data.supports_ptz ? 'Available' : 'Not Available';
        ptzSupport.className = `status-value ${data.supports_ptz ? 'success' : 'error'}`;

        // Update tracking status
        const trackingStatus = document.getElementById('tracking-status');
        trackingStatus.textContent = data.tracking_enabled ? 'Active' : 'Disabled';
        trackingStatus.className = `status-value ${data.tracking_enabled ? 'success' : 'error'}`;

        // Update auto-tracking checkbox
        document.getElementById('auto-tracking').checked = data.tracking_enabled;

        // Update position display
        this.updatePTZPosition({
            pan: data.current_pan,
            tilt: data.current_tilt,
            zoom: data.current_zoom
        });

        // Enable/disable PTZ controls based on support
        const ptzButtons = document.querySelectorAll('.ptz-btn');
        ptzButtons.forEach(btn => {
            btn.disabled = !data.supports_ptz;
            btn.style.opacity = data.supports_ptz ? '1' : '0.5';
        });
    }

    updatePTZPosition(data) {
        if (data.pan !== undefined) {
            document.getElementById('current-pan').textContent = `${data.pan.toFixed(1)}°`;
        }
        if (data.tilt !== undefined) {
            document.getElementById('current-tilt').textContent = `${data.tilt.toFixed(1)}°`;
        }
        if (data.zoom !== undefined) {
            document.getElementById('zoom-level').textContent = `${data.zoom.toFixed(1)}x`;
        }
    }
}

// Initialize the UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DroneDetectionUI();
});
