/* Thermal Drone Detection System Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.status-item {
    background: rgba(255,255,255,0.1);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.status-label {
    font-weight: bold;
    margin-right: 10px;
}

.status-value {
    color: #4CAF50;
}

.video-section {
    margin-bottom: 30px;
}

.video-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.video-panel {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.video-panel h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #FFD700;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 300px;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
}

.video-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.detection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.controls-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.control-panel, .stats-panel {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.control-panel h3, .stats-panel h3 {
    margin-bottom: 20px;
    color: #FFD700;
}

.control-group {
    margin-bottom: 20px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    margin-right: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #f44336;
    color: white;
}

.btn-secondary:hover {
    background: #da190b;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ff9800;
    color: white;
}

.btn-warning:hover {
    background: #e68900;
    transform: translateY(-2px);
}

label {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
}

input[type="checkbox"] {
    margin-right: 10px;
}

input[type="range"] {
    width: 100%;
    margin: 10px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    background: rgba(0,0,0,0.2);
    padding: 15px;
    border-radius: 10px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4CAF50;
}

.alerts-section {
    margin-bottom: 30px;
}

.alerts-panel {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.alerts-panel h3 {
    margin-bottom: 20px;
    color: #FFD700;
}

.alerts-list {
    max-height: 200px;
    overflow-y: auto;
}

.alert-item {
    background: rgba(244, 67, 54, 0.2);
    border-left: 4px solid #f44336;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert-time {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-bottom: 5px;
}

.alert-message {
    font-weight: bold;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    margin: 15% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 500px;
    text-align: center;
    position: relative;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 15px;
    right: 20px;
    cursor: pointer;
}

.close:hover {
    color: #fff;
}

.detection-box {
    position: absolute;
    border: 3px solid #ff0000;
    background: rgba(255, 0, 0, 0.1);
    pointer-events: none;
}

.detection-label {
    position: absolute;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 3px;
    top: -25px;
    left: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .video-container {
        grid-template-columns: 1fr;
    }
    
    .controls-section {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        flex-direction: column;
        align-items: center;
    }
}
