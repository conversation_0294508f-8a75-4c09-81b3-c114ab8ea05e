<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thermal Drone Detection System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚁 Thermal Drone Detection System</h1>
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-label">System Status:</span>
                    <span id="system-status" class="status-value">Initializing...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Camera:</span>
                    <span id="camera-status" class="status-value">Connecting...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Alarm:</span>
                    <span id="alarm-status" class="status-value">Ready</span>
                </div>
            </div>
        </header>

        <main>
            <div class="video-section">
                <div class="video-container">
                    <div class="video-panel">
                        <h3>Visible Camera</h3>
                        <div class="video-wrapper">
                            <img id="visible-feed" src="" alt="Visible Camera Feed">
                            <div class="detection-overlay" id="visible-overlay"></div>
                        </div>
                    </div>
                    <div class="video-panel">
                        <h3>Thermal Camera</h3>
                        <div class="video-wrapper">
                            <img id="thermal-feed" src="" alt="Thermal Camera Feed">
                            <div class="detection-overlay" id="thermal-overlay"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="controls-section">
                <div class="control-panel">
                    <h3>Detection Controls</h3>
                    <div class="control-group">
                        <button id="start-detection" class="btn btn-primary">Start Detection</button>
                        <button id="stop-detection" class="btn btn-secondary">Stop Detection</button>
                        <button id="test-alarm" class="btn btn-warning">Test Alarm</button>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="alarm-enabled" checked> Enable Alarm
                        </label>
                        <label>
                            <input type="checkbox" id="save-detections" checked> Save Detections
                        </label>
                    </div>
                    <div class="control-group">
                        <label for="confidence-threshold">Confidence Threshold:</label>
                        <input type="range" id="confidence-threshold" min="0.1" max="1.0" step="0.1" value="0.6">
                        <span id="confidence-value">0.6</span>
                    </div>
                </div>

                <div class="stats-panel">
                    <h3>Detection Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Total Detections:</span>
                            <span id="total-detections" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Recent (1 min):</span>
                            <span id="recent-detections" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Detection:</span>
                            <span id="last-detection" class="stat-value">Never</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Detection Rate:</span>
                            <span id="detection-rate" class="stat-value">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alerts-section">
                <div class="alerts-panel">
                    <h3>Recent Alerts</h3>
                    <div id="alerts-list" class="alerts-list">
                        <!-- Alerts will be populated here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <div id="alert-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>🚨 DRONE DETECTED!</h2>
            <div id="alert-details"></div>
            <button id="acknowledge-alert" class="btn btn-primary">Acknowledge</button>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
