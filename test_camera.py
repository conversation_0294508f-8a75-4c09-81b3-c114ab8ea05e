#!/usr/bin/env python3
"""
Quick camera connectivity test script
"""

import requests
import cv2
from config import *

def test_http_endpoints():
    """Test HTTP endpoints for camera connectivity"""
    print("🔍 Testing HTTP CGI endpoints...")
    
    session = requests.Session()
    session.auth = (CAMERA_USERNAME, CAMERA_PASSWORD)
    
    print(f"\n📹 Testing Visible Camera URLs:")
    for i, url in enumerate(CAMERA_HTTP_URLS[:5], 1):  # Test first 5 URLs
        try:
            print(f"  {i}. Testing: {url}")
            response = session.get(url, timeout=5)
            print(f"     Status: {response.status_code}")
            if response.status_code == 200:
                print(f"     ✅ SUCCESS - Content-Type: {response.headers.get('content-type', 'unknown')}")
                print(f"     Content-Length: {len(response.content)} bytes")
                break
            else:
                print(f"     ❌ Failed with status {response.status_code}")
        except Exception as e:
            print(f"     ❌ Error: {e}")
    
    print(f"\n🌡️ Testing Thermal Camera URLs:")
    for i, url in enumerate(THERMAL_HTTP_URLS[:5], 1):  # Test first 5 URLs
        try:
            print(f"  {i}. Testing: {url}")
            response = session.get(url, timeout=5)
            print(f"     Status: {response.status_code}")
            if response.status_code == 200:
                print(f"     ✅ SUCCESS - Content-Type: {response.headers.get('content-type', 'unknown')}")
                print(f"     Content-Length: {len(response.content)} bytes")
                break
            else:
                print(f"     ❌ Failed with status {response.status_code}")
        except Exception as e:
            print(f"     ❌ Error: {e}")

def test_rtsp_streams():
    """Test RTSP stream connectivity"""
    print(f"\n📡 Testing RTSP Streams...")
    
    rtsp_urls = [
        CAMERA_STREAM_URL,
        THERMAL_STREAM_URL
    ]
    
    for i, url in enumerate(rtsp_urls, 1):
        try:
            print(f"  {i}. Testing: {url}")
            cap = cv2.VideoCapture(url)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"     ✅ SUCCESS - Frame shape: {frame.shape}")
                else:
                    print(f"     ❌ Failed to read frame")
            else:
                print(f"     ❌ Failed to open stream")
            cap.release()
        except Exception as e:
            print(f"     ❌ Error: {e}")

def test_basic_connectivity():
    """Test basic network connectivity to camera"""
    print(f"\n🌐 Testing Basic Connectivity to {CAMERA_IP}...")
    
    try:
        # Test basic HTTP connectivity
        response = requests.get(f"http://{CAMERA_IP}", timeout=5)
        print(f"  HTTP Status: {response.status_code}")
        print(f"  Server: {response.headers.get('server', 'unknown')}")
    except Exception as e:
        print(f"  ❌ HTTP Error: {e}")
    
    try:
        # Test with authentication
        response = requests.get(f"http://{CAMERA_IP}", 
                              auth=(CAMERA_USERNAME, CAMERA_PASSWORD), 
                              timeout=5)
        print(f"  HTTP Auth Status: {response.status_code}")
    except Exception as e:
        print(f"  ❌ HTTP Auth Error: {e}")

if __name__ == "__main__":
    print("🚁 THERMAL CAMERA CONNECTIVITY TEST")
    print("=" * 50)
    print(f"Camera IP: {CAMERA_IP}")
    print(f"Username: {CAMERA_USERNAME}")
    print(f"Password: {'*' * len(CAMERA_PASSWORD)}")
    print("=" * 50)
    
    test_basic_connectivity()
    test_http_endpoints()
    test_rtsp_streams()
    
    print("\n✅ Test completed!")
