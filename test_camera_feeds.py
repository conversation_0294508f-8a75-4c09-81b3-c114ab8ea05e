"""
Simple camera feed test script to verify both thermal and visible camera connections
"""

import cv2
import numpy as np
import time
import sys
from config import *

def test_camera_feeds():
    """Test and display both camera feeds"""
    print("🔍 Testing Camera Feeds")
    print("=" * 50)
    print(f"Camera IP: {CAMERA_IP}")
    print(f"Username: {CAMERA_USERNAME}")
    print("=" * 50)
    
    # Test URLs for your camera
    test_urls = {
        'visible': [
            f"http://{CAMERA_IP}/video",
            f"http://{CAMERA_IP}/mjpeg", 
            f"http://{CAMERA_IP}:8080/video",
            f"http://{CAMERA_IP}/cgi-bin/mjpg/video.cgi",
            f"http://{CAMERA_IP}/videostream.cgi",
            f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/stream1"
        ],
        'thermal': [
            f"http://{CAMERA_IP}/thermal",
            f"http://{CAMERA_IP}/thermal_mjpeg",
            f"http://{CAMERA_IP}:8080/thermal", 
            f"http://{CAMERA_IP}/infrared",
            f"http://{CAMERA_IP}/ir_stream",
            f"rtsp://{CAMERA_USERNAME}:{CAMERA_PASSWORD}@{CAMERA_IP}/thermal"
        ]
    }
    
    working_feeds = {}
    
    # Test each camera type
    for camera_type, urls in test_urls.items():
        print(f"\n🎥 Testing {camera_type.upper()} camera feeds...")
        
        for i, url in enumerate(urls):
            print(f"  [{i+1}/{len(urls)}] Trying: {url}")
            
            try:
                cap = cv2.VideoCapture(url)
                
                if cap.isOpened():
                    # Try to read a frame
                    ret, frame = cap.read()
                    
                    if ret and frame is not None:
                        height, width = frame.shape[:2]
                        print(f"    ✅ SUCCESS! Resolution: {width}x{height}")
                        working_feeds[camera_type] = {
                            'url': url,
                            'cap': cap,
                            'resolution': (width, height)
                        }
                        break
                    else:
                        print(f"    ❌ Stream opened but no frames received")
                        cap.release()
                else:
                    print(f"    ❌ Failed to open stream")
                    
            except Exception as e:
                print(f"    ❌ Error: {e}")
            
            time.sleep(0.5)  # Brief pause between attempts
    
    # Display results
    print(f"\n📊 RESULTS:")
    print("=" * 50)
    
    if working_feeds:
        for camera_type, info in working_feeds.items():
            print(f"✅ {camera_type.upper()}: {info['url']}")
            print(f"   Resolution: {info['resolution'][0]}x{info['resolution'][1]}")
        
        # Show live feeds
        print(f"\n🖥️  Displaying live feeds...")
        print("Press 'q' to quit, 's' to save frames")
        
        show_live_feeds(working_feeds)
        
    else:
        print("❌ No working camera feeds found!")
        print("\n💡 Troubleshooting tips:")
        print("1. Verify camera IP address and credentials")
        print("2. Check if camera is powered on and connected")
        print("3. Ensure camera supports MJPEG or RTSP streaming")
        print("4. Try accessing camera web interface manually")
        print("5. Check firewall settings")
    
    # Cleanup
    for info in working_feeds.values():
        info['cap'].release()
    cv2.destroyAllWindows()

def show_live_feeds(working_feeds):
    """Display live camera feeds"""
    frame_count = 0
    
    while True:
        frames_to_show = []
        
        # Read frames from all working cameras
        for camera_type, info in working_feeds.items():
            ret, frame = info['cap'].read()
            
            if ret and frame is not None:
                # Resize frame for display
                display_frame = cv2.resize(frame, (640, 480))
                
                # Add camera type label
                cv2.putText(display_frame, f"{camera_type.upper()} Camera", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # Add frame info
                cv2.putText(display_frame, f"Frame: {frame_count}", 
                           (10, 460), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                frames_to_show.append((camera_type, display_frame))
        
        # Display frames
        if frames_to_show:
            if len(frames_to_show) == 1:
                # Single camera
                cv2.imshow(f"{frames_to_show[0][0]} Camera", frames_to_show[0][1])
            else:
                # Multiple cameras - show side by side
                combined_frame = np.hstack([frame for _, frame in frames_to_show])
                cv2.imshow("Camera Feeds", combined_frame)
        
        frame_count += 1
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q'):
            print("Quitting...")
            break
        elif key == ord('s'):
            # Save current frames
            timestamp = int(time.time())
            for camera_type, frame in frames_to_show:
                filename = f"{camera_type}_frame_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"Saved: {filename}")
        
        time.sleep(0.033)  # ~30 FPS

def test_single_url():
    """Test a single URL provided by user"""
    print("\n🔧 Manual URL Test")
    print("=" * 30)
    
    url = input("Enter camera URL to test: ").strip()
    if not url:
        return
    
    print(f"Testing: {url}")
    
    try:
        cap = cv2.VideoCapture(url)
        
        if cap.isOpened():
            ret, frame = cap.read()
            
            if ret and frame is not None:
                height, width = frame.shape[:2]
                print(f"✅ SUCCESS! Resolution: {width}x{height}")
                
                # Show frame
                cv2.imshow("Test Frame", frame)
                print("Press any key to close...")
                cv2.waitKey(0)
                cv2.destroyAllWindows()
            else:
                print("❌ Stream opened but no frames received")
        else:
            print("❌ Failed to open stream")
            
        cap.release()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    print("🚁 Thermal Camera Feed Tester")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Auto-test camera feeds")
        print("2. Test specific URL")
        print("3. Exit")
        
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == '1':
            test_camera_feeds()
        elif choice == '2':
            test_single_url()
        elif choice == '3':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please select 1, 2, or 3.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        cv2.destroyAllWindows()
    except Exception as e:
        print(f"\nError: {e}")
        cv2.destroyAllWindows()
