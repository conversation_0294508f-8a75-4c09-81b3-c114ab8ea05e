#!/usr/bin/env python3
"""
Test video feeds directly
"""

import cv2
import time
from config import *

def test_video_feeds():
    """Test both video feeds and save sample frames"""
    print("🎥 Testing Video Feeds...")
    
    # Test visible camera
    print(f"\n📹 Testing Visible Camera: {CAMERA_STREAM_URL}")
    visible_cap = cv2.VideoCapture(CAMERA_STREAM_URL)
    
    if visible_cap.isOpened():
        ret, frame = visible_cap.read()
        if ret and frame is not None:
            print(f"✅ Visible camera working - Frame shape: {frame.shape}")
            cv2.imwrite("test_visible_frame.jpg", frame)
            print("📸 Saved test_visible_frame.jpg")
        else:
            print("❌ Failed to read visible frame")
    else:
        print("❌ Failed to open visible camera")
    
    visible_cap.release()
    
    # Test thermal camera
    print(f"\n🌡️ Testing Thermal Camera: {THERMAL_STREAM_URL}")
    thermal_cap = cv2.VideoCapture(THERMAL_STREAM_URL)
    
    if thermal_cap.isOpened():
        ret, frame = thermal_cap.read()
        if ret and frame is not None:
            print(f"✅ Thermal camera working - Frame shape: {frame.shape}")
            cv2.imwrite("test_thermal_frame.jpg", frame)
            print("📸 Saved test_thermal_frame.jpg")
        else:
            print("❌ Failed to read thermal frame")
    else:
        print("❌ Failed to open thermal camera")
    
    thermal_cap.release()

def test_continuous_stream():
    """Test continuous streaming for 10 seconds"""
    print(f"\n🔄 Testing Continuous Stream (10 seconds)...")
    
    visible_cap = cv2.VideoCapture(CAMERA_STREAM_URL)
    thermal_cap = cv2.VideoCapture(THERMAL_STREAM_URL)
    
    start_time = time.time()
    frame_count = 0
    
    while time.time() - start_time < 10:
        # Read visible frame
        if visible_cap.isOpened():
            ret_v, frame_v = visible_cap.read()
            if ret_v:
                frame_count += 1
        
        # Read thermal frame  
        if thermal_cap.isOpened():
            ret_t, frame_t = thermal_cap.read()
            if ret_t:
                frame_count += 1
        
        time.sleep(0.1)  # 10 FPS
    
    visible_cap.release()
    thermal_cap.release()
    
    fps = frame_count / 10
    print(f"📊 Average FPS: {fps:.1f}")
    print(f"📊 Total frames: {frame_count}")

if __name__ == "__main__":
    print("🚁 VIDEO FEED TEST")
    print("=" * 40)
    
    test_video_feeds()
    test_continuous_stream()
    
    print("\n✅ Video feed test completed!")
