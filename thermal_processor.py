"""
Thermal image processing utilities for enhanced drone detection
"""

import cv2
import numpy as np
from scipy import ndimage
from skimage import filters, morphology
import logging
from config import *

class ThermalProcessor:
    def __init__(self):
        self.background_model = None
        self.frame_count = 0
        self.logger = logging.getLogger(__name__)
        
    def preprocess_thermal(self, thermal_frame):
        """Preprocess thermal frame for better drone detection"""
        if thermal_frame is None:
            return None
            
        try:
            # Convert to grayscale if needed
            if len(thermal_frame.shape) == 3:
                thermal_gray = cv2.cvtColor(thermal_frame, cv2.COLOR_BGR2GRAY)
            else:
                thermal_gray = thermal_frame.copy()
            
            # Noise reduction
            if THERMAL_NOISE_REDUCTION:
                thermal_gray = cv2.bilateralFilter(thermal_gray, 9, 75, 75)
            
            # Contrast enhancement
            if THERMAL_CONTRAST_ENHANCEMENT:
                thermal_gray = cv2.equalizeHist(thermal_gray)
                # Additional CLAHE for local contrast
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
                thermal_gray = clahe.apply(thermal_gray)
            
            return thermal_gray
            
        except Exception as e:
            self.logger.error(f"Error preprocessing thermal frame: {e}")
            return thermal_frame
    
    def detect_hot_objects(self, thermal_frame):
        """Detect hot objects in thermal frame that could be drones"""
        if thermal_frame is None:
            return []
            
        try:
            # Preprocess the frame
            processed = self.preprocess_thermal(thermal_frame)
            
            # Background subtraction
            if BACKGROUND_SUBTRACTION:
                processed = self.subtract_background(processed)
            
            # Threshold for hot objects
            # Assuming higher pixel values = hotter objects
            threshold_value = np.mean(processed) + THERMAL_TEMPERATURE_THRESHOLD * np.std(processed)
            _, binary = cv2.threshold(processed, threshold_value, 255, cv2.THRESH_BINARY)
            
            # Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by size and shape
            hot_objects = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if MIN_DRONE_SIZE <= area <= MAX_DRONE_SIZE:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    
                    # Drones typically have aspect ratios between 0.5 and 2.0
                    if 0.3 <= aspect_ratio <= 3.0:
                        # Calculate confidence based on thermal signature
                        roi = processed[y:y+h, x:x+w]
                        avg_temp = np.mean(roi)
                        confidence = min(1.0, (avg_temp - np.mean(processed)) / (3 * np.std(processed)))
                        
                        hot_objects.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': max(0.0, confidence),
                            'avg_temperature': avg_temp,
                            'contour': contour
                        })
            
            return hot_objects
            
        except Exception as e:
            self.logger.error(f"Error detecting hot objects: {e}")
            return []
    
    def subtract_background(self, frame):
        """Subtract background to highlight moving/new objects"""
        if self.background_model is None:
            self.background_model = frame.astype(np.float32)
            return np.zeros_like(frame)
        
        # Update background model slowly
        alpha = 0.01
        cv2.accumulateWeighted(frame, self.background_model, alpha)
        
        # Subtract background
        diff = cv2.absdiff(frame, self.background_model.astype(np.uint8))
        
        return diff
    
    def enhance_thermal_contrast(self, thermal_frame):
        """Enhance thermal contrast for better visualization"""
        if thermal_frame is None:
            return None
            
        try:
            # Convert to grayscale if needed
            if len(thermal_frame.shape) == 3:
                gray = cv2.cvtColor(thermal_frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = thermal_frame.copy()
            
            # Apply histogram equalization
            equalized = cv2.equalizeHist(gray)
            
            # Apply false color mapping for better visualization
            colored = cv2.applyColorMap(equalized, cv2.COLORMAP_JET)
            
            return colored
            
        except Exception as e:
            self.logger.error(f"Error enhancing thermal contrast: {e}")
            return thermal_frame
    
    def combine_visible_thermal(self, visible_frame, thermal_frame, alpha=0.6):
        """Combine visible and thermal frames for enhanced detection"""
        if visible_frame is None or thermal_frame is None:
            return visible_frame if visible_frame is not None else thermal_frame
            
        try:
            # Resize frames to match
            h, w = visible_frame.shape[:2]
            thermal_resized = cv2.resize(thermal_frame, (w, h))
            
            # Enhance thermal for overlay
            thermal_enhanced = self.enhance_thermal_contrast(thermal_resized)
            
            # Blend frames
            combined = cv2.addWeighted(visible_frame, 1-alpha, thermal_enhanced, alpha, 0)
            
            return combined
            
        except Exception as e:
            self.logger.error(f"Error combining frames: {e}")
            return visible_frame
