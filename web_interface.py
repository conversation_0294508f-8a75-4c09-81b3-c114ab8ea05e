"""
Web interface for the thermal drone detection system
"""

from flask import <PERSON>lask, render_template, jsonify, request
from flask_socketio import <PERSON>cket<PERSON>, emit
import cv2
import base64
import threading
import time
import logging
from datetime import datetime
import json
from config import *
from camera_interface import ThermalCameraInterface
from drone_detector import DroneDetector
from alarm_system import AlarmSystem
from thermal_processor import ThermalProcessor

class WebInterface:
    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'thermal_drone_detection_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Initialize components
        self.camera = ThermalCameraInterface()
        self.detector = DroneDetector()
        self.alarm = AlarmSystem()
        self.thermal_processor = ThermalProcessor()
        
        # State variables
        self.detection_running = False
        self.detection_thread = None
        self.frame_count = 0
        
        self.logger = logging.getLogger(__name__)
        self.setup_routes()
        self.setup_socket_events()
    
    def setup_routes(self):
        @self.app.route('/')
        def index():
            return render_template('index.html')
        
        @self.app.route('/api/status')
        def get_status():
            return jsonify({
                'detection_running': self.detection_running,
                'camera_connected': self.camera.visible_cap is not None,
                'alarm_enabled': ALARM_ENABLED,
                'stats': self.detector.get_detection_stats()
            })
    
    def setup_socket_events(self):
        @self.socketio.on('connect')
        def handle_connect():
            self.logger.info('Client connected')
            emit('camera_status', {
                'status': 'Connected' if self.camera.visible_cap else 'Disconnected',
                'connected': self.camera.visible_cap is not None
            })
            emit('detection_status', {'running': self.detection_running})
            emit('alarm_status', self.alarm.get_status())
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            self.logger.info('Client disconnected')
        
        @self.socketio.on('start_detection')
        def handle_start_detection():
            if not self.detection_running:
                self.start_detection()
                emit('detection_status', {'running': True})
        
        @self.socketio.on('stop_detection')
        def handle_stop_detection():
            if self.detection_running:
                self.stop_detection()
                emit('detection_status', {'running': False})
        
        @self.socketio.on('test_alarm')
        def handle_test_alarm():
            result = self.alarm.test_alarm()
            emit('alarm_status', self.alarm.get_status())
        
        @self.socketio.on('set_alarm_enabled')
        def handle_set_alarm_enabled(enabled):
            self.alarm.set_enabled(enabled)
            emit('alarm_status', self.alarm.get_status())
        
        @self.socketio.on('set_confidence_threshold')
        def handle_set_confidence_threshold(threshold):
            global DETECTION_CONFIDENCE_THRESHOLD
            DETECTION_CONFIDENCE_THRESHOLD = threshold
            self.logger.info(f"Confidence threshold set to {threshold}")
        
        @self.socketio.on('get_status')
        def handle_get_status():
            emit('stats_update', self.detector.get_detection_stats())
            emit('alarm_status', self.alarm.get_status())
        
        @self.socketio.on('acknowledge_alert')
        def handle_acknowledge_alert():
            self.alarm.stop_alarm()
    
    def start_detection(self):
        """Start the detection process"""
        if self.detection_running:
            return
        
        self.logger.info("Starting detection system...")
        
        # Connect to camera
        if not self.camera.connect():
            self.logger.error("Failed to connect to camera")
            return
        
        self.detection_running = True
        self.detection_thread = threading.Thread(target=self.detection_loop)
        self.detection_thread.daemon = True
        self.detection_thread.start()
        
        self.logger.info("Detection system started")
    
    def stop_detection(self):
        """Stop the detection process"""
        if not self.detection_running:
            return
        
        self.logger.info("Stopping detection system...")
        self.detection_running = False
        
        if self.detection_thread:
            self.detection_thread.join(timeout=5)
        
        self.camera.disconnect()
        self.logger.info("Detection system stopped")
    
    def detection_loop(self):
        """Main detection loop"""
        last_frame_time = 0
        frame_interval = 1.0 / MAX_FPS
        
        while self.detection_running:
            try:
                current_time = time.time()
                
                # Control frame rate
                if current_time - last_frame_time < frame_interval:
                    time.sleep(0.01)
                    continue
                
                last_frame_time = current_time
                
                # Get frames from camera
                visible_frame, thermal_frame = self.camera.get_frames()
                
                # Skip frame processing if configured
                self.frame_count += 1
                if self.frame_count % FRAME_SKIP != 0:
                    continue
                
                # Process frames
                if visible_frame is not None or thermal_frame is not None:
                    self.process_frames(visible_frame, thermal_frame)
                
            except Exception as e:
                self.logger.error(f"Error in detection loop: {e}")
                time.sleep(1)
    
    def process_frames(self, visible_frame, thermal_frame):
        """Process frames for detection and streaming"""
        try:
            # Resize frames if needed
            if visible_frame is not None and RESIZE_FACTOR != 1.0:
                h, w = visible_frame.shape[:2]
                new_h, new_w = int(h * RESIZE_FACTOR), int(w * RESIZE_FACTOR)
                visible_frame = cv2.resize(visible_frame, (new_w, new_h))
            
            if thermal_frame is not None and RESIZE_FACTOR != 1.0:
                h, w = thermal_frame.shape[:2]
                new_h, new_w = int(h * RESIZE_FACTOR), int(w * RESIZE_FACTOR)
                thermal_frame = cv2.resize(thermal_frame, (new_w, new_h))
            
            # Run detection
            detections = self.detector.detect(visible_frame, thermal_frame)
            
            # Handle detections
            if detections:
                self.handle_detections(detections, visible_frame, thermal_frame)
            
            # Stream frames to web interface
            self.stream_frames(visible_frame, thermal_frame, detections)
            
        except Exception as e:
            self.logger.error(f"Error processing frames: {e}")
    
    def handle_detections(self, detections, visible_frame, thermal_frame):
        """Handle detected drones"""
        try:
            # Filter high-confidence detections
            high_conf_detections = [
                d for d in detections 
                if d['confidence'] >= DETECTION_CONFIDENCE_THRESHOLD
            ]
            
            if high_conf_detections:
                # Trigger alarm
                detection_info = {
                    'timestamp': datetime.now().isoformat(),
                    'detection_count': len(high_conf_detections),
                    'max_confidence': max(d['confidence'] for d in high_conf_detections),
                    'detections': high_conf_detections
                }
                
                if self.alarm.trigger_alarm(detection_info):
                    # Send alarm to web interface
                    self.socketio.emit('alarm_triggered', {
                        'timestamp': detection_info['timestamp'],
                        'detection_count': detection_info['detection_count'],
                        'max_confidence': detection_info['max_confidence'],
                        'zone': high_conf_detections[0].get('zone', 'Unknown')
                    })
                
                # Send detection results to web interface
                self.socketio.emit('detection_result', {
                    'detections': high_conf_detections,
                    'frame_type': 'visible',
                    'frame_width': visible_frame.shape[1] if visible_frame is not None else 640,
                    'frame_height': visible_frame.shape[0] if visible_frame is not None else 480
                })
                
        except Exception as e:
            self.logger.error(f"Error handling detections: {e}")
    
    def stream_frames(self, visible_frame, thermal_frame, detections):
        """Stream frames to web interface"""
        try:
            frame_data = {}
            
            # Encode visible frame
            if visible_frame is not None:
                # Draw detection boxes
                display_frame = visible_frame.copy()
                for detection in detections:
                    x, y, w, h = detection['bbox']
                    confidence = detection['confidence']
                    label = f"{detection['class']}: {confidence:.2f}"
                    
                    cv2.rectangle(display_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                    cv2.putText(display_frame, label, (x, y-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                _, buffer = cv2.imencode('.jpg', display_frame)
                frame_data['visible_frame'] = base64.b64encode(buffer).decode('utf-8')
            
            # Encode thermal frame
            if thermal_frame is not None:
                # Enhance thermal frame for display
                enhanced_thermal = self.thermal_processor.enhance_thermal_contrast(thermal_frame)
                
                # Draw detection boxes on thermal
                for detection in detections:
                    if detection.get('source') == 'thermal':
                        x, y, w, h = detection['bbox']
                        cv2.rectangle(enhanced_thermal, (x, y), (x+w, y+h), (0, 0, 255), 2)
                
                _, buffer = cv2.imencode('.jpg', enhanced_thermal)
                frame_data['thermal_frame'] = base64.b64encode(buffer).decode('utf-8')
            
            # Send frames to web interface
            if frame_data:
                self.socketio.emit('video_frame', frame_data)
                
        except Exception as e:
            self.logger.error(f"Error streaming frames: {e}")
    
    def run(self):
        """Run the web interface"""
        self.logger.info(f"Starting web interface on {WEB_HOST}:{WEB_PORT}")
        self.socketio.run(self.app, host=WEB_HOST, port=WEB_PORT, debug=False)
    
    def shutdown(self):
        """Shutdown the web interface"""
        self.stop_detection()
        self.alarm.stop_alarm()

if __name__ == '__main__':
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run web interface
    web_interface = WebInterface()
    try:
        web_interface.run()
    except KeyboardInterrupt:
        web_interface.shutdown()
